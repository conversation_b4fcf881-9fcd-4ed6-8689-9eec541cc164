chcp 65001
@echo off
echo 正在清理旧的打包文件...
if exist "dist" rmdir /s /q "dist"
if exist "build" rmdir /s /q "build"

echo 正在打包到exe (onedir模式)...
pyinstaller --onedir --noconsole ^
    --add-data "static;static" ^
    --add-data "config.json;." ^
    --hidden-import "PIL._tkinter_finder" ^
    --hidden-import "pystray._win32" ^
    --hidden-import "webview.platforms.winforms" ^
    --hidden-import "schedule" ^
    --hidden-import "apscheduler.schedulers.background" ^
    --hidden-import "psutil" ^
    --hidden-import "threading" ^
    --hidden-import "subprocess" ^
    --name "AutoShutdown" ^
    main.py

echo 复制Python文件到dist目录...
copy "settings_window.py" "dist\AutoShutdown\"
copy "shutdown_overlay_window.py" "dist\AutoShutdown\"
copy "config_manager.py" "dist\AutoShutdown\"
copy "scheduler_manager.py" "dist\AutoShutdown\"
copy "tray_manager.py" "dist\AutoShutdown\"
copy "window_manager.py" "dist\AutoShutdown\"

echo 打包完成,exe文件在 dist\AutoShutdown 文件夹中
echo 可以运行 dist\AutoShutdown\AutoShutdown.exe
pause
