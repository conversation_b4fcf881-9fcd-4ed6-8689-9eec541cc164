// Shutdown Overlay JavaScript with iPhone-style slide to cancel

class ShutdownOverlay {
    constructor() {
        this.countdownDuration = 30;
        this.currentCountdown = this.countdownDuration;
        this.countdownInterval = null;
        this.slideButton = null;
        this.slideTrack = null;
        this.isDragging = false;
        this.startX = 0;
        this.currentX = 0;
        this.slideThreshold = 0.8; // 80% of track width to trigger cancel
        this.sentences = []; // 存储加载的句子数据

        this.init();
    }

    async init() {
        // 显示loading状态
        this.showLoadingState();

        try {
            // 获取倒计时时长
            this.countdownDuration = await pywebview.api.get_countdown_duration();
            this.currentCountdown = this.countdownDuration;
        } catch (error) {
            console.error('Failed to get countdown duration:', error);
            // 使用默认值
            this.countdownDuration = 30;
            this.currentCountdown = this.countdownDuration;
        }

        // 设置元素和事件监听器
        this.setupElements();
        this.setupEventListeners();

        // 初始化时不调用，等待pywebview准备好

        // 更新倒计时显示
        this.updateCountdownDisplay();

        // 隐藏loading，显示主要内容
        this.hideLoadingState();

        // 倒计时将在hideLoadingState中通过window_loaded API调用启动
    }

    async displayRandomSentence() {
        try {
            // 从Python API获取随机句子
            const sentenceData = await pywebview.api.get_random_sentence();

            // 显示句子
            const sentenceContent = document.getElementById('sentence-content');
            const sentenceMeta = document.getElementById('sentence-meta');

            if (sentenceContent && sentenceData && sentenceData.content) {
                sentenceContent.textContent = sentenceData.content;
            }

            if (sentenceMeta && sentenceData && sentenceData.title && sentenceData.author) {
                sentenceMeta.textContent = `—— ${sentenceData.title} · ${sentenceData.author}`;
            }
        } catch (error) {
            console.error('Failed to get random sentence:', error);
            // 静默失败，保持Loading状态
        }
    }

    setupElements() {
        this.slideButton = document.getElementById('slide-button');
        this.slideTrack = document.querySelector('.slide-track');
        this.countdownTimer = document.getElementById('countdown-timer');
    }

    setupEventListeners() {
        // Mouse events
        this.slideButton.addEventListener('mousedown', this.handleStart.bind(this));
        document.addEventListener('mousemove', this.handleMove.bind(this));
        document.addEventListener('mouseup', this.handleEnd.bind(this));

        // Touch events for mobile-like experience
        this.slideButton.addEventListener('touchstart', this.handleStart.bind(this), { passive: false });
        document.addEventListener('touchmove', this.handleMove.bind(this), { passive: false });
        document.addEventListener('touchend', this.handleEnd.bind(this));

        // Prevent context menu
        this.slideButton.addEventListener('contextmenu', (e) => e.preventDefault());
    }

    handleStart(event) {
        event.preventDefault();
        this.isDragging = true;
        
        const clientX = event.type === 'mousedown' ? event.clientX : event.touches[0].clientX;
        this.startX = clientX - this.slideButton.offsetLeft;
        
        this.slideButton.style.transition = 'none';
        this.slideTrack.classList.add('dragging');
        
        document.body.style.userSelect = 'none';
    }

    handleMove(event) {
        if (!this.isDragging) return;
        
        event.preventDefault();
        
        const clientX = event.type === 'mousemove' ? event.clientX : event.touches[0].clientX;
        this.currentX = clientX - this.startX;
        
        const trackWidth = this.slideTrack.offsetWidth;
        const buttonWidth = this.slideButton.offsetWidth;
        const maxX = trackWidth - buttonWidth;
        
        // Constrain movement within track bounds
        this.currentX = Math.max(0, Math.min(this.currentX, maxX));
        
        // Update button position
        this.slideButton.style.left = this.currentX + 'px';
        
        // Update visual feedback based on progress
        const progress = this.currentX / maxX;
        this.updateSlideProgress(progress);
    }

    handleEnd() {
        if (!this.isDragging) return;
        
        this.isDragging = false;
        document.body.style.userSelect = '';
        
        const trackWidth = this.slideTrack.offsetWidth;
        const buttonWidth = this.slideButton.offsetWidth;
        const maxX = trackWidth - buttonWidth;
        const progress = this.currentX / maxX;
        
        this.slideTrack.classList.remove('dragging');
        
        if (progress >= this.slideThreshold) {
            // Slide completed - cancel shutdown
            this.cancelShutdown();
        } else {
            // Slide not completed - reset position
            this.resetSlidePosition();
        }
    }

    updateSlideProgress(progress) {
        const slideBackground = document.querySelector('.slide-background');
        const slideText = document.querySelector('.slide-text');
        
        // Update background color based on progress
        const greenIntensity = Math.min(progress * 2, 1);
        slideBackground.style.backgroundColor = `rgba(34, 197, 94, ${greenIntensity * 0.3})`;
        
        // Update text opacity
        slideText.style.opacity = Math.max(1 - progress * 2, 0.3);
        
        // Add glow effect when near completion
        if (progress > 0.7) {
            this.slideButton.style.boxShadow = `0 0 20px rgba(34, 197, 94, ${(progress - 0.7) * 3})`;
        } else {
            this.slideButton.style.boxShadow = '';
        }
    }

    resetSlidePosition() {
        this.slideButton.style.transition = 'left 0.3s ease-out, box-shadow 0.3s ease-out';
        this.slideButton.style.left = '0px';
        this.slideButton.style.boxShadow = '';
        
        const slideBackground = document.querySelector('.slide-background');
        const slideText = document.querySelector('.slide-text');
        
        slideBackground.style.backgroundColor = '';
        slideText.style.opacity = '';
        
        // Remove transition after animation
        setTimeout(() => {
            this.slideButton.style.transition = 'none';
        }, 300);
    }

    async cancelShutdown() {
        // Animate completion
        this.slideButton.style.transition = 'left 0.2s ease-out';
        const trackWidth = this.slideTrack.offsetWidth;
        const buttonWidth = this.slideButton.offsetWidth;
        this.slideButton.style.left = (trackWidth - buttonWidth) + 'px';
        
        // Update visual feedback
        this.updateSlideProgress(1);
        
        // Show success feedback
        const slideText = document.querySelector('.slide-text');
        slideText.textContent = 'Shutdown Cancelled!';
        slideText.style.color = '#22c55e';
        slideText.style.opacity = '1';
        
        // Stop countdown
        if (this.countdownInterval) {
            clearInterval(this.countdownInterval);
        }
        
        // Call Python API to cancel shutdown
        try {
            await pywebview.api.cancel_shutdown();
        } catch (error) {
            console.error('Failed to cancel shutdown:', error);
        }
        
        // Close overlay after brief delay
        setTimeout(() => {
            window.close();
        }, 1000);
    }

    startCountdown() {
        this.countdownInterval = setInterval(() => {
            this.currentCountdown--;
            this.updateCountdownDisplay();
            
            if (this.currentCountdown <= 0) {
                clearInterval(this.countdownInterval);
                // Shutdown will be handled by Python
            }
        }, 1000);
    }

    updateCountdownDisplay() {
        if (this.countdownTimer) {
            this.countdownTimer.textContent = this.currentCountdown;

            // Add urgency visual effects
            if (this.currentCountdown <= 10) {
                this.countdownTimer.style.color = '#ef4444';
                this.countdownTimer.style.animation = 'pulse 1s infinite';
            } else if (this.currentCountdown <= 20) {
                this.countdownTimer.style.color = '#f59e0b';
            }
        }
    }

    showLoadingState() {
        const loadingOverlay = document.getElementById('loading-overlay');
        const warningContent = document.getElementById('warning-content');

        if (loadingOverlay) {
            loadingOverlay.classList.remove('hidden');
        }

        if (warningContent) {
            warningContent.classList.remove('ready');
        }
    }

    hideLoadingState() {
        const loadingOverlay = document.getElementById('loading-overlay');
        const warningContent = document.getElementById('warning-content');

        // 添加一个小延迟确保所有元素都已准备好
        setTimeout(async () => {
            if (loadingOverlay) {
                loadingOverlay.classList.add('hidden');
            }

            if (warningContent) {
                warningContent.classList.add('ready');
            }

            try {
                await pywebview.api.window_loaded();
                await this.displayRandomSentence();
            } catch (error) {
                console.error('Failed to notify window loaded:', error);
                this.startCountdown();
            }
        }, 1000); 
    }
}

// Global function for Python to call
function updateCountdown(remaining) {
    const timer = document.getElementById('countdown-timer');
    if (timer) {
        timer.textContent = remaining;
        
        if (remaining <= 10) {
            timer.style.color = '#ef4444';
            timer.style.animation = 'pulse 1s infinite';
        } else if (remaining <= 20) {
            timer.style.color = '#f59e0b';
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // 立即显示loading状态
    const loadingOverlay = document.getElementById('loading-overlay');
    if (loadingOverlay) {
        loadingOverlay.classList.remove('hidden');
    }

    // 创建主应用实例
    new ShutdownOverlay();
});
