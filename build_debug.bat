@echo off
echo Cleaning old build files...
if exist "dist" rmdir /s /q "dist"
if exist "build" rmdir /s /q "build"

echo Building exe with console mode for debugging...
pyinstaller --onedir --console --add-data "static;static" --add-data "config.json;." --hidden-import "PIL._tkinter_finder" --hidden-import "pystray._win32" --hidden-import "webview.platforms.winforms" --hidden-import "schedule" --hidden-import "apscheduler.schedulers.background" --hidden-import "psutil" --hidden-import "threading" --hidden-import "subprocess" --name "AutoShutdown" main.py

echo Copying Python files to dist directory...
copy "settings_window.py" "dist\AutoShutdown\"
copy "shutdown_overlay_window.py" "dist\AutoShutdown\"
copy "config_manager.py" "dist\AutoShutdown\"
copy "scheduler_manager.py" "dist\AutoShutdown\"
copy "tray_manager.py" "dist\AutoShutdown\"
copy "window_manager.py" "dist\AutoShutdown\"

echo Build complete, exe file is in dist\AutoShutdown folder
echo Run dist\AutoShutdown\AutoShutdown.exe to see debug info
pause
