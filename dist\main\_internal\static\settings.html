<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>自动关机设置</title>
    <style>
        :root {
            --bg-color: #fafafa;
            --card-bg: #ffffff;
            --text-primary: #2d2d2d;
            --text-secondary: #6b7280;
            --border-color: #e5e7eb;
            --accent-color: #3b82f6;
            --success-color: #22c55e;
            --danger-color: #ef4444;
            --radius: 8px;
            --font-size-base: 14px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--bg-color);
            color: var(--text-primary);
            font-size: var(--font-size-base);
            line-height: 1.5;
            padding: 16px;
        }

        .container {
            max-width: 640px;
            margin: 0 auto;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            margin-bottom: 16px;
        }

        .header h1 {
            font-size: 18px;
            font-weight: 600;
        }

        .card {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: var(--radius);
            padding: 16px;
            margin-bottom: 16px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .card-header h2 {
            font-size: 16px;
            font-weight: 600;
        }

        .schedules-list {
            display: grid;
            gap: 8px;
        }

        .schedule-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px;
            border: 1px solid var(--border-color);
            border-radius: var(--radius);
            transition: background 0.2s;
        }

        .schedule-item:hover {
            background: #f9fafb;
        }

        .schedule-info {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .schedule-info strong {
            font-weight: 500;
        }

        .schedule-info span {
            color: var(--text-secondary);
        }

        .schedule-actions {
            display: flex;
            gap: 8px;
        }

        .no-schedules {
            text-align: center;
            padding: 16px;
            color: var(--text-secondary);
            border: 1px dashed var(--border-color);
            border-radius: var(--radius);
        }

        .settings-list {
            display: grid;
            gap: 8px;
        }

        .setting-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: var(--radius);
        }

        .setting-item label {
            font-weight: 500;
        }

        .setting-item input[type="number"] {
            width: 60px;
            padding: 4px;
            border: 1px solid var(--border-color);
            border-radius: var(--radius);
            font-size: var(--font-size-base);
            text-align: center;
        }

        .setting-item input[type="number"]:focus {
            outline: none;
            border-color: var(--accent-color);
        }

        .toggle-switch {
            position: relative;
            width: 32px;
            height: 16px;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--border-color);
            border-radius: 9999px;
            transition: background 0.2s;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 12px;
            width: 12px;
            left: 2px;
            bottom: 2px;
            background: #fff;
            border-radius: 50%;
            transition: transform 0.2s;
        }

        .toggle-switch input:checked + .slider {
            background: var(--success-color);
        }

        .toggle-switch input:checked + .slider:before {
            transform: translateX(16px);
        }

        .btn {
            padding: 6px 12px;
            border: none;
            border-radius: var(--radius);
            font-size: var(--font-size-base);
            font-weight: 500;
            cursor: pointer;
            transition: background 0.2s;
        }

        .btn-primary {
            background: var(--accent-color);
            color: #fff;
        }

        .btn-primary:hover {
            background: #2563eb;
        }

        .btn-success {
            background: var(--success-color);
            color: #fff;
        }

        .btn-success:hover {
            background: #16a34a;
        }

        .btn-danger {
            background: var(--danger-color);
            color: #fff;
        }

        .btn-danger:hover {
            background: #dc2626;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: var(--card-bg);
            width: 90%;
            max-width: 400px;
            border-radius: var(--radius);
            padding: 16px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .modal-header h3 {
            font-size: 16px;
            font-weight: 600;
        }

        .modal-close {
            font-size: 18px;
            color: var(--text-secondary);
            cursor: pointer;
            transition: color 0.2s;
        }

        .modal-close:hover {
            color: var(--text-primary);
        }

        .form-group {
            margin-bottom: 12px;
        }

        .form-group label {
            display: block;
            font-size: var(--font-size-base);
            font-weight: 500;
            margin-bottom: 4px;
        }

        .form-group input[type="text"] {
            width: 100%;
            padding: 6px;
            border: 1px solid var(--border-color);
            border-radius: var(--radius);
            font-size: var(--font-size-base);
        }

        .form-group input:focus {
            outline: none;
            border-color: var(--accent-color);
        }

        .time-picker {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .time-picker input {
            width: 50px;
            padding: 4px;
            border: 1px solid var(--border-color);
            border-radius: var(--radius);
            font-size: var(--font-size-base);
            text-align: center;
        }

        .days-selector {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .day-checkbox {
            padding: 4px 8px;
            border: 1px solid var(--border-color);
            border-radius: var(--radius);
            cursor: pointer;
            font-size: var(--font-size-base);
            transition: all 0.2s;
        }

        .day-checkbox input {
            display: none;
        }

        .day-checkbox:has(input:checked) {
            background: var(--accent-color);
            color: #fff;
            border-color: var(--accent-color);
        }

        .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 8px;
            margin-top: 16px;
        }

        @media (max-width: 480px) {
            .container {
                padding: 8px;
            }

            .schedule-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
            }

            .schedule-actions {
                width: 100%;
                justify-content: flex-end;
            }

            .modal-content {
                width: 95%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>自动关机设置</h1>
            <button id="save-btn" class="btn btn-success">保存</button>
        </header>

        <div class="card">
            <div class="card-header">
                <h2>关机计划</h2>
                <button id="add-schedule-btn" class="btn btn-primary">添加</button>
            </div>
            <div id="schedules-list" class="schedules-list"></div>
        </div>

        <div class="card">
            <div class="card-header">
                <h2>通用设置</h2>
            </div>
            <div class="settings-list">
                <div class="setting-item">
                    <label for="countdown-duration">倒计时 (秒)</label>
                    <input type="number" id="countdown-duration" min="10" max="300" value="30">
                </div>
                <div class="setting-item">
                    <label for="enable-notifications">启用通知</label>
                    <label class="toggle-switch">
                        <input type="checkbox" id="enable-notifications" checked>
                        <span class="slider"></span>
                    </label>
                </div>
            </div>
        </div>
    </div>

    <div id="modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-title">添加计划</h3>
                <span class="modal-close" id="modal-close">×</span>
            </div>
            <div class="form-group">
                <label for="schedule-name">计划名称</label>
                <input type="text" id="schedule-name" placeholder="例如：工作日关机">
            </div>
            <div class="form-group">
                <label>时间</label>
                <div class="time-picker">
                    <input type="number" id="schedule-hour" min="0" max="23" value="22">
                    <span>:</span>
                    <input type="number" id="schedule-minute" min="0" max="59" value="28">
                </div>
            </div>
            <div class="form-group">
                <label>星期</label>
                <div class="days-selector">
                    <label class="day-checkbox"><input type="checkbox" value="monday">周一</label>
                    <label class="day-checkbox"><input type="checkbox" value="tuesday">周二</label>
                    <label class="day-checkbox"><input type="checkbox" value="wednesday">周三</label>
                    <label class="day-checkbox"><input type="checkbox" value="thursday">周四</label>
                    <label class="day-checkbox"><input type="checkbox" value="friday">周五</label>
                    <label class="day-checkbox"><input type="checkbox" value="saturday">周六</label>
                    <label class="day-checkbox"><input type="checkbox" value="sunday">周日</label>
                </div>
            </div>
            <div class="form-group">
                <div class="setting-item">
                    <label for="schedule-enabled">启用计划</label>
                    <label class="toggle-switch">
                        <input type="checkbox" id="schedule-enabled" checked>
                        <span class="slider"></span>
                    </label>
                </div>
            </div>
            <div class="modal-footer">
                <button id="cancel-schedule-btn" class="btn btn-success">取消</button>
                <button id="save-schedule-btn" class="btn btn-primary">保存</button>
            </div>
        </div>
    </div>

    <script>
        class SettingsManager {
            constructor() {
                this.config = { schedules: [], settings: { countdown_duration: 30, enable_notifications: true } };
                this.currentEditingSchedule = null;
                this.isPywebviewReady = false;
            }

            async init() {
                await this.waitForPywebview();
                this.setupEventListeners();
                await this.loadConfig();
                this.renderSchedules();
                this.loadSettings();
            }

            waitForPywebview(timeout = 5000) {
                return new Promise((resolve, reject) => {
                    if (window.pywebview && window.pywebview.api) {
                        this.isPywebviewReady = true;
                        resolve();
                        return;
                    }

                    const startTime = Date.now();
                    const checkReady = () => {
                        if (window.pywebview && window.pywebview.api) {
                            this.isPywebviewReady = true;
                            resolve();
                        } else if (Date.now() - startTime > timeout) {
                            console.warn('pywebview not ready after timeout, using default config');
                            resolve();
                        } else {
                            setTimeout(checkReady, 100);
                        }
                    };

                    window.addEventListener('pywebviewready', () => {
                        this.isPywebviewReady = true;
                        resolve();
                    }, { once: true });

                    checkReady();
                });
            }

            async loadConfig() {
                if (!this.isPywebviewReady) {
                    console.warn('pywebview not ready, using default config');
                    return;
                }
                try {
                    this.config = await window.pywebview.api.get_config();
                    console.log('Config loaded:', this.config);
                } catch (error) {
                    console.error('Failed to load config:', error);
                    alert('无法加载配置，使用默认设置');
                }
            }

            setupEventListeners() {
                document.getElementById('add-schedule-btn').addEventListener('click', () => this.showModal());
                document.getElementById('save-btn').addEventListener('click', () => this.saveSettings());
                document.getElementById('modal-close').addEventListener('click', () => this.hideModal());
                document.getElementById('cancel-schedule-btn').addEventListener('click', () => this.hideModal());
                document.getElementById('save-schedule-btn').addEventListener('click', () => this.saveSchedule());
                window.addEventListener('click', (event) => {
                    if (event.target === document.getElementById('modal')) this.hideModal();
                });
            }

            renderSchedules() {
                const list = document.getElementById('schedules-list');
                list.innerHTML = '';
                if (this.config.schedules.length === 0) {
                    list.innerHTML = '<div class="no-schedules">暂无计划</div>';
                    return;
                }
                this.config.schedules.forEach(schedule => {
                    const item = document.createElement('div');
                    item.className = 'schedule-item';
                    item.innerHTML = `
                        <div class="schedule-info">
                            <strong>${schedule.name}</strong>
                            <span>时间: ${schedule.time} | 星期: ${schedule.days.join(', ')}</span>
                        </div>
                        <div class="schedule-actions">
                            <button class="btn btn-primary" onclick="settingsManager.editSchedule(${schedule.id})">编辑</button>
                            <button class="btn btn-danger" onclick="settingsManager.deleteSchedule(${schedule.id})">删除</button>
                        </div>
                    `;
                    list.appendChild(item);
                });
            }

            showModal(schedule = null) {
                this.currentEditingSchedule = schedule;
                document.getElementById('modal-title').textContent = schedule ? '编辑计划' : '添加计划';
                if (schedule) {
                    document.getElementById('schedule-name').value = schedule.name;
                    const [hour, minute] = schedule.time.split(':');
                    document.getElementById('schedule-hour').value = parseInt(hour, 10);
                    document.getElementById('schedule-minute').value = parseInt(minute, 10);
                    document.querySelectorAll('.days-selector input').forEach(input => {
                        input.checked = schedule.days.includes(input.value);
                    });
                    document.getElementById('schedule-enabled').checked = schedule.enabled;
                } else {
                    document.getElementById('schedule-name').value = '';
                    document.getElementById('schedule-hour').value = '22';
                    document.getElementById('schedule-minute').value = '0';
                    document.querySelectorAll('.days-selector input').forEach(input => input.checked = false);
                    document.getElementById('schedule-enabled').checked = true;
                }
                document.getElementById('modal').style.display = 'flex';
            }

            hideModal() {
                document.getElementById('modal').style.display = 'none';
            }

            saveSchedule() {
                const name = document.getElementById('schedule-name').value.trim();
                const hour = parseInt(document.getElementById('schedule-hour').value, 10);
                const minute = parseInt(document.getElementById('schedule-minute').value, 10);
                const days = Array.from(document.querySelectorAll('.days-selector input:checked')).map(input => input.value);
                const enabled = document.getElementById('schedule-enabled').checked;

                if (!name) {
                    alert('请填写计划名称');
                    return;
                }
                if (days.length === 0) {
                    alert('请至少选择一天');
                    return;
                }
                if (isNaN(hour) || hour < 0 || hour > 23 || isNaN(minute) || minute < 0 || minute > 59) {
                    alert('请输入有效时间（小时：0-23，分钟：0-59）');
                    return;
                }

                const time = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
                const schedule = { name, time, days, enabled };

                if (this.currentEditingSchedule) {
                    schedule.id = this.currentEditingSchedule.id;
                    const index = this.config.schedules.findIndex(s => s.id === schedule.id);
                    this.config.schedules[index] = schedule;
                } else {
                    schedule.id = Date.now();
                    this.config.schedules.push(schedule);
                }

                this.renderSchedules();
                this.hideModal();
            }

            editSchedule(id) {
                const schedule = this.config.schedules.find(s => s.id === id);
                this.showModal(schedule);
            }

            deleteSchedule(id) {
                if (confirm('确定删除此计划吗？')) {
                    this.config.schedules = this.config.schedules.filter(s => s.id !== id);
                    this.renderSchedules();
                }
            }

            loadSettings() {
                const settings = this.config.settings;
                document.getElementById('countdown-duration').value = settings.countdown_duration || 30;
                document.getElementById('enable-notifications').checked = settings.enable_notifications !== false;
            }

            async saveSettings() {
                const countdown = parseInt(document.getElementById('countdown-duration').value, 10);
                if (isNaN(countdown) || countdown < 10 || countdown > 300) {
                    alert('倒计时必须在10到300秒之间');
                    return;
                }

                this.config.settings = {
                    countdown_duration: countdown,
                    enable_notifications: document.getElementById('enable-notifications').checked
                };

                if (!this.isPywebviewReady) {
                    alert('pywebview 未就绪，无法保存设置');
                    return;
                }

                try {
                    const result = await window.pywebview.api.save_config(this.config);
                    alert(result.success ? '设置已保存' : '保存失败');
                } catch (error) {
                    console.error('Failed to save settings:', error);
                    alert('保存失败');
                }
            }
        }

        document.addEventListener('DOMContentLoaded', () => {
            const settingsManager = new SettingsManager();
            window.settingsManager = settingsManager;
            settingsManager.init();
        });
    </script>
</body>
</html>