@echo off
echo 正在打包到exe...

REM 清理旧文件
if exist dist rmdir /s /q dist
if exist build rmdir /s /q build
if exist *.spec del *.spec

REM 安装依赖
pip install pyinstaller

REM 一步打包
pyinstaller --onedir --windowed --add-data "static;static" --add-data "config.json;." --hidden-import "PIL._tkinter_finder" --hidden-import "pystray._win32" --hidden-import "webview.platforms.winforms" --hidden-import "schedule" --hidden-import "apscheduler.schedulers.background" --hidden-import "psutil" main.py

echo.
echo 打包完成！exe文件在 dist\main 文件夹中
pause
