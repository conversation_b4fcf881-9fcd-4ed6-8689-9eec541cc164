chcp 65001
@echo off
echo 正在清理旧的打包文件...
if exist "dist" rmdir /s /q "dist"
if exist "build" rmdir /s /q "build"

echo 正在打包到exe...
pyinstaller --onefile --noconsole ^
    --add-data "static;static" ^
    --add-data "config.json;." ^
    --add-data "settings_window.py;." ^
    --add-data "shutdown_overlay_window.py;." ^
    --add-data "config_manager.py;." ^
    --add-data "scheduler_manager.py;." ^
    --add-data "tray_manager.py;." ^
    --add-data "window_manager.py;." ^
    --hidden-import "PIL._tkinter_finder" ^
    --hidden-import "pystray._win32" ^
    --hidden-import "webview.platforms.winforms" ^
    --hidden-import "schedule" ^
    --hidden-import "apscheduler.schedulers.background" ^
    --hidden-import "psutil" ^
    --hidden-import "threading" ^
    --hidden-import "subprocess" ^
    --name "AutoShutdown" ^
    main.py

echo 打包完成,exe文件在 dist 文件夹中
pause
