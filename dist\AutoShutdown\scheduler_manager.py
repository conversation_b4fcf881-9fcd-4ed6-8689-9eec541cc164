"""
Scheduler Manager for Auto Shutdown Application
Handles scheduling and execution of shutdown tasks
"""

import sys
import subprocess
import time
from datetime import datetime
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger


class SchedulerManager:
    def __init__(self, config_manager, window_manager):
        """
        Initialize the scheduler manager

        Args:
            config_manager: ConfigManager instance
            window_manager: WindowManager instance
        """
        self.config_manager = config_manager
        self.window_manager = window_manager
        self.scheduler = BackgroundScheduler()
        self.scheduler.start()

        # Load and apply existing configuration on startup
        self.reload_schedules()

    def reload_schedules(self):
        """Reload schedules from configuration - public method for external calls"""
        self.setup_scheduled_shutdowns()
        print("Schedules reloaded from configuration")

    def setup_scheduled_shutdowns(self):
        """Setup scheduled shutdowns based on configuration"""
        # Clear existing jobs
        self.scheduler.remove_all_jobs()

        config = self.config_manager.load_config()
        schedules = config.get('schedules', [])

        if not schedules:
            print("No schedules found in configuration")
            return

        active_count = 0
        for schedule_item in schedules:
            if schedule_item.get('enabled', True):
                self.add_scheduled_shutdown(schedule_item)
                active_count += 1

        print(f"Loaded {active_count} active schedules out of {len(schedules)} total schedules")

    def add_scheduled_shutdown(self, schedule_item):
        """Add a scheduled shutdown job"""
        days = schedule_item.get('days', [])
        time_str = schedule_item.get('time', '22:00')
        
        try:
            hour, minute = map(int, time_str.split(':'))
        except ValueError:
            print(f"Invalid time format: {time_str}")
            return
        
        # Convert day names to cron format
        day_mapping = {
            'monday': 0, 'tuesday': 1, 'wednesday': 2, 'thursday': 3,
            'friday': 4, 'saturday': 5, 'sunday': 6
        }
        
        cron_days = [str(day_mapping[day.lower()]) for day in days if day.lower() in day_mapping]
        
        if cron_days:
            trigger = CronTrigger(
                day_of_week=','.join(cron_days),
                hour=hour,
                minute=minute
            )
            
            self.scheduler.add_job(
                self.trigger_shutdown,
                trigger,
                id=f"shutdown_{schedule_item.get('id', 'default')}"
            )
            print(f"Added shutdown schedule: {schedule_item.get('name', 'Unnamed')} at {time_str} on {', '.join(days)}")

    def trigger_shutdown(self):
        """Trigger shutdown with overlay"""
        print(f"Shutdown triggered at {datetime.now()}")

        try:
            # Show shutdown overlay through window manager
            self.window_manager.show_shutdown_overlay()
        except Exception as e:
            print(f"Failed to show shutdown overlay: {e}")
            # Fallback - execute shutdown directly after delay
            time.sleep(30)
            self.execute_shutdown()

    def execute_shutdown(self):
        """Execute system shutdown"""
        print(f"Executing shutdown at {datetime.now()}")
        try:
            # Windows shutdown command with immediate shutdown
            if sys.platform == "win32":
                subprocess.run(['shutdown', '/s', '/f', '/t', '0'], check=True)
            elif sys.platform in ["linux", "darwin"]:
                # For other platforms (testing)
                subprocess.run(['sudo', 'shutdown', '-h', 'now'], check=True)
            else:
                print(f"Unsupported platform: {sys.platform}")
        except subprocess.CalledProcessError as e:
            print(f"Failed to shutdown: {e}")
        except FileNotFoundError:
            print("Shutdown command not found - running in test mode")

    def get_active_jobs(self):
        """Get list of active scheduled jobs"""
        return self.scheduler.get_jobs()

    def remove_job(self, job_id):
        """Remove a specific scheduled job"""
        try:
            self.scheduler.remove_job(job_id)
            return True
        except Exception as e:
            print(f"Failed to remove job {job_id}: {e}")
            return False

    def stop(self):
        """Stop the scheduler"""
        if self.scheduler:
            self.scheduler.shutdown()
            print("Scheduler stopped")
