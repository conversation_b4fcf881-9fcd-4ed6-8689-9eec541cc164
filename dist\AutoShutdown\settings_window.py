"""
Settings Window for Auto Shutdown Application
Runs as a separate process to avoid threading issues with pywebview
"""

import webview
import sys
import os
import time
from pathlib import Path
from config_manager import ConfigManager


class SettingsAPI:
    def __init__(self):
        self.config_manager = ConfigManager()
        self.reload_signal_file = Path("scheduler_reload.signal")

    def get_config(self):
        """Get current configuration for web interface"""
        config = self.config_manager.load_config()
        print(f"API: Returning config: {config}")
        return config

    def save_config(self, new_config):
        """Save configuration from web interface"""
        success = self.config_manager.save_config(new_config)

        if success:
            # Create a signal file to notify main app to reload schedules
            try:
                self.reload_signal_file.write_text(str(time.time()))
                print("Configuration saved and reload signal sent")
            except Exception as e:
                print(f"Failed to create reload signal: {e}")

        return {"success": success}


def main():
    """Run the settings window"""
    try:
        # Create API instance
        api = SettingsAPI()

        # Get the correct path to the HTML file
        base_path = os.path.dirname(os.path.abspath(__file__))
        html_path = os.path.join(base_path, 'static', 'settings.html')

        # Verify the HTML file exists
        if not os.path.exists(html_path):
            print(f"HTML file not found: {html_path}")
            sys.exit(1)

        print(f"Loading settings window from: {html_path}")

        # Create settings window
        window = webview.create_window(
            'Auto Shutdown Settings',
            html_path,
            width=800,
            height=600,
            resizable=True,
            js_api=api
        )

        # Start webview
        webview.start()

    except Exception as e:
        print(f"Settings window error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
