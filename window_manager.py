"""
Window Manager for Auto Shutdown Application
Handles creation and management of settings and shutdown overlay windows
"""

import os
import sys
import subprocess


class WindowManager:
    def __init__(self):
        """Initialize the window manager"""
        # Determine if we're running from a PyInstaller bundle
        if getattr(sys, 'frozen', False):
            # Running from PyInstaller bundle
            if hasattr(sys, '_MEIPASS'):
                # onefile mode - scripts are in temp directory
                self.base_path = getattr(sys, '_MEIPASS', '')
            else:
                # onedir mode - scripts are in the same directory as exe
                self.base_path = os.path.dirname(sys.executable)
            self.is_bundled = True
        else:
            # Running from source
            self.base_path = os.path.dirname(os.path.abspath(__file__))
            self.is_bundled = False

    def _get_python_executable(self):
        """Get the correct Python executable path"""
        if self.is_bundled:
            # In bundled mode, we need to use the current executable
            return sys.executable
        else:
            # In development mode, use the current Python interpreter
            return sys.executable

    def _get_script_path(self, script_name):
        """Get the correct path to a script file"""
        if self.is_bundled:
            # In bundled mode, scripts are extracted to the temp directory
            return os.path.join(self.base_path, script_name)
        else:
            # In development mode, scripts are in the current directory
            return os.path.join(self.base_path, script_name)

    def show_settings_window(self):
        """Show settings window in a separate process"""
        try:
            script_path = self._get_script_path('settings_window.py')
            python_exe = self._get_python_executable()

            print(f"Attempting to run: {python_exe} {script_path}")
            print(f"Working directory: {self.base_path}")

            # Create subprocess with proper environment
            env = os.environ.copy()
            if self.is_bundled:
                # Add the bundle directory to Python path
                env['PYTHONPATH'] = self.base_path

            process = subprocess.Popen([
                python_exe,
                script_path
            ], cwd=self.base_path, env=env)

            print(f"Settings window process started with PID: {process.pid}")
        except Exception as e:
            print(f"Failed to open settings window: {e}")
            import traceback
            traceback.print_exc()

    def show_shutdown_overlay(self):
        """Show shutdown overlay window in a separate process"""
        try:
            script_path = self._get_script_path('shutdown_overlay_window.py')
            python_exe = self._get_python_executable()

            print(f"Attempting to run: {python_exe} {script_path}")
            print(f"Working directory: {self.base_path}")

            # Create subprocess with proper environment
            env = os.environ.copy()
            if self.is_bundled:
                # Add the bundle directory to Python path
                env['PYTHONPATH'] = self.base_path

            process = subprocess.Popen([
                python_exe,
                script_path
            ], cwd=self.base_path, env=env)

            print(f"Shutdown overlay process started with PID: {process.pid}")
        except Exception as e:
            print(f"Failed to show shutdown overlay: {e}")
            import traceback
            traceback.print_exc()



    def get_base_path(self):
        """Get the base path for the application"""
        return self.base_path
